<script setup lang="ts">
import { cn } from '@/lib/utils';


defineProps<{
    title: string,
    tags: {
        name: string,
        text: string,
        href: string
    }[],
    activeTagName: string
}>()
</script>

<template>
    <div class="flex flex-col mt-8">
        <h1 class="font-semibold text-foreground-title">
            {{ title }}
        </h1>
        <ul class="flex items-center flex-wrap gap-2 mt-4">
            <li v-for="item in tags" :key="item.name">
                <RouterLink :to="item.href" :class="cn('rounded-full px-2 py-1.5 bg-gray-200 text-foreground-muted', {
                    'bg-primary text-white': activeTagName === item.name
                })">
                    {{ item.text }}
                </RouterLink>
            </li>
        </ul>
    </div>
</template>
